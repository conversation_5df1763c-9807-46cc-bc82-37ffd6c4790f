from pydantic import BaseModel, Field
from typing import List, Optional

class Item(BaseModel):
    name: str = Field(description="Name or description of the item")
    category: str = Field(description="Category of the item", enum=[
        "Food & Dining", "Transportation", "Utilities", "Entertainment",
        "Shopping", "Healthcare", "Education", "Travel", "Housing", "Other"
    ])
    price: float = Field(description="Price of the item")
    quantity: int = Field(description="Quantity of the item")

class Receipt(BaseModel):
    response: Optional[str] = None
    items: List[Item] = Field(description="List of items in the receipt")
    date: str = Field(description="Date of the purchase in dd/mm/yyyy format")
    time: Optional[str] = Field(description="Time of the purchase (optional)")
    shop_name: Optional[str] = Field(description="Name of the shop (optional)")
    total: float = Field(description="Total amount of the purchase")