import flet as ft
import base64
from flet.matplotlib_chart import MatplotlibChart
import matplotlib.pyplot as plt
import pandas as pd
from utils import process_receipt_image
from database import add_expense, get_all_expenses, update_expense, delete_expense, init_db

class AppUI(ft.UserControl):
    def __init__(self, page):
        super().__init__()
        self.page = page
        init_db()
        self.build()

    def build(self):
        self.expense_list = ft.ListView(expand=True, spacing=10)
        self.update_expense_list()

        self.main_view = ft.Column(
            [
                ft.Text("Expenses", size=30, weight=ft.FontWeight.BOLD),
                self.expense_list,
                ft.FloatingActionButton(
                    icon=ft.icons.ADD, on_click=self.show_add_expense_dialog
                ),
            ],
            expand=True,
        )
        return self.main_view

    def update_expense_list(self):
        self.expense_list.controls.clear()
        expenses = get_all_expenses()
        for expense in expenses:
            self.expense_list.controls.append(
                ft.ListTile(
                    title=ft.Text(f"{expense[1]} - ${expense[3]:.2f}"),
                    subtitle=ft.Text(f"{expense[2]} on {expense[5]}"),
                    on_click=lambda e, expense_id=expense[0]: self.show_edit_expense_dialog(expense_id),
                    trailing=ft.IconButton(
                        ft.icons.DELETE,
                        on_click=lambda e, expense_id=expense[0]: self.delete_expense_item(expense_id)
                    )
                )
            )
        self.update()

    def show_add_expense_dialog(self, e):
        self.name_field = ft.TextField(label="Name")
        self.category_field = ft.Dropdown(
            label="Category",
            options=[
                ft.dropdown.Option("Food & Dining"),
                ft.dropdown.Option("Transportation"),
                ft.dropdown.Option("Utilities"),
                ft.dropdown.Option("Entertainment"),
                ft.dropdown.Option("Shopping"),
                ft.dropdown.Option("Healthcare"),
                ft.dropdown.Option("Education"),
                ft.dropdown.Option("Travel"),
                ft.dropdown.Option("Housing"),
                ft.dropdown.Option("Other"),
            ]
        )
        self.price_field = ft.TextField(label="Price", keyboard_type=ft.KeyboardType.NUMBER)
        self.quantity_field = ft.TextField(label="Quantity", keyboard_type=ft.KeyboardType.NUMBER)
        self.date_field = ft.TextField(label="Date (dd/mm/yyyy)")

        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Add Expense"),
            content=ft.Column([
                self.name_field,
                self.category_field,
                self.price_field,
                self.quantity_field,
                self.date_field,
                ft.FilePicker(on_result=self.scan_receipt),
            ]),
            actions=[
                ft.TextButton("Scan Receipt", on_click=lambda _: self.page.overlay[0].pick_files()),
                ft.TextButton("Save", on_click=self.add_manual_expense),
                ft.TextButton("Cancel", on_click=self.close_dialog),
            ],
        )
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()

    def add_manual_expense(self, e):
        add_expense(
            self.name_field.value,
            self.category_field.value,
            float(self.price_field.value),
            int(self.quantity_field.value),
            self.date_field.value
        )
        self.close_dialog(e)
        self.update_expense_list()

    def scan_receipt(self, e: ft.FilePickerResultEvent):
        if e.files:
            file_path = e.files[0].path
            with open(file_path, "rb") as f:
                image_data = base64.b64encode(f.read()).decode("utf-8")

            receipt_data = process_receipt_image(image_data)
            if receipt_data:
                for item in receipt_data.items:
                    add_expense(
                        item.name,
                        item.category,
                        item.price,
                        item.quantity,
                        receipt_data.date,
                        receipt_data.time,
                        receipt_data.shop_name
                    )
            self.update_expense_list()
            self.close_dialog(e)

    def show_edit_expense_dialog(self, expense_id):
        expenses = get_all_expenses()
        expense = next((exp for exp in expenses if exp[0] == expense_id), None)
        if not expense:
            return

        self.edit_name_field = ft.TextField(label="Name", value=expense[1])
        self.edit_category_field = ft.Dropdown(
            label="Category",
            value=expense[2],
            options=[
                ft.dropdown.Option("Food & Dining"),
                ft.dropdown.Option("Transportation"),
                ft.dropdown.Option("Utilities"),
                ft.dropdown.Option("Entertainment"),
                ft.dropdown.Option("Shopping"),
                ft.dropdown.Option("Healthcare"),
                ft.dropdown.Option("Education"),
                ft.dropdown.Option("Travel"),
                ft.dropdown.Option("Housing"),
                ft.dropdown.Option("Other"),
            ]
        )
        self.edit_price_field = ft.TextField(label="Price", value=str(expense[3]))
        self.edit_quantity_field = ft.TextField(label="Quantity", value=str(expense[4]))
        self.edit_date_field = ft.TextField(label="Date (dd/mm/yyyy)", value=expense[5])

        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Edit Expense"),
            content=ft.Column([
                self.edit_name_field,
                self.edit_category_field,
                self.edit_price_field,
                self.edit_quantity_field,
                self.edit_date_field,
            ]),
            actions=[
                ft.TextButton("Save", on_click=lambda e: self.save_edited_expense(expense_id)),
                ft.TextButton("Cancel", on_click=self.close_dialog),
            ],
        )
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()

    def save_edited_expense(self, expense_id):
        update_expense(
            expense_id,
            self.edit_name_field.value,
            self.edit_category_field.value,
            float(self.edit_price_field.value),
            int(self.edit_quantity_field.value),
            self.edit_date_field.value
        )
        self.close_dialog(None)
        self.update_expense_list()

    def delete_expense_item(self, expense_id):
        delete_expense(expense_id)
        self.update_expense_list()

    def close_dialog(self, e):
        self.dialog.open = False
        self.page.update()

    def show_reports(self, e):
        expenses = get_all_expenses()
        if not expenses:
            return

        df = pd.DataFrame(expenses, columns=['id', 'name', 'category', 'price', 'quantity', 'date', 'time', 'shop_name'])
        df['date'] = pd.to_datetime(df['date'], format='%d/%m/%Y')
        df['total_price'] = df['price'] * df['quantity']

        # Monthly Breakdown
        monthly_summary = df.resample('M', on='date')['total_price'].sum()
        plt.figure(figsize=(10, 5))
        monthly_summary.plot(kind='bar')
        plt.title('Monthly Expense Breakdown')
        plt.xlabel('Month')
        plt.ylabel('Total Expense')
        monthly_chart = MatplotlibChart(plt)

        # Weekly Breakdown
        weekly_summary = df.resample('W', on='date')['total_price'].sum()
        plt.figure(figsize=(10, 5))
        weekly_summary.plot(kind='line')
        plt.title('Weekly Expense Breakdown')
        plt.xlabel('Week')
        plt.ylabel('Total Expense')
        weekly_chart = MatplotlibChart(plt)

        # Category Totals
        category_summary = df.groupby('category')['total_price'].sum()
        plt.figure(figsize=(10, 5))
        category_summary.plot(kind='pie', autopct='%1.1f%%')
        plt.title('Expense by Category')
        plt.ylabel('')
        category_chart = MatplotlibChart(plt)

        self.page.views.append(
            ft.View(
                "/reports",
                [
                    ft.AppBar(title=ft.Text("Reports"), bgcolor=ft.colors.SURFACE_VARIANT),
                    monthly_chart,
                    weekly_chart,
                    category_chart,
                ],
            )
        )
        self.page.go("/reports")