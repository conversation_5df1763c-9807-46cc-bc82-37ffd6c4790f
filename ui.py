import flet as ft
import base64
from datetime import datetime
import pandas as pd
from utils import process_receipt_image
from database import add_expense, get_all_expenses, update_expense, delete_expense, init_db

class ExpenseTrackerApp(ft.Column):
    def __init__(self, page: ft.Page):
        super().__init__()
        self.page = page
        self.current_tab = 0
        init_db()

        # Initialize file picker for receipt scanning
        self.file_picker = ft.FilePicker(on_result=self.on_file_picked)
        self.page.overlay.append(self.file_picker)

        # Loading state
        self.is_loading = False

        # Build the UI
        self.build_ui()

    def build_ui(self):
        # Create the main navigation
        self.navigation_bar = ft.NavigationBar(
            destinations=[
                ft.NavigationDestination(
                    icon=ft.icons.CAMERA_ALT_OUTLINED,
                    selected_icon=ft.icons.CAMERA_ALT,
                    label="Scan"
                ),
                ft.NavigationDestination(
                    icon=ft.icons.LIST_OUTLINED,
                    selected_icon=ft.icons.LIST,
                    label="Expenses"
                ),
                ft.NavigationDestination(
                    icon=ft.icons.ANALYTICS_OUTLINED,
                    selected_icon=ft.icons.ANALYTICS,
                    label="Reports"
                ),
                ft.NavigationDestination(
                    icon=ft.icons.ADD_OUTLINED,
                    selected_icon=ft.icons.ADD,
                    label="Add"
                ),
            ],
            selected_index=0,
            on_change=self.on_tab_change,
        )

        # Create content container
        self.content_container = ft.Container(
            content=self.build_scan_tab(),
            expand=True,
            padding=ft.padding.all(16),
        )

        # Set up the column properties
        self.controls = [
            self.content_container,
            self.navigation_bar,
        ]
        self.spacing = 0
        self.expand = True

    def on_tab_change(self, e):
        self.current_tab = e.control.selected_index

        if self.current_tab == 0:  # Scan tab
            self.content_container.content = self.build_scan_tab()
        elif self.current_tab == 1:  # Expenses tab
            self.content_container.content = self.build_expenses_tab()
        elif self.current_tab == 2:  # Reports tab
            self.content_container.content = self.build_reports_tab()
        elif self.current_tab == 3:  # Add tab
            self.content_container.content = self.build_add_tab()

        self.update()

    def build_scan_tab(self):
        return ft.Column(
            [
                ft.Container(
                    content=ft.Column(
                        [
                            ft.Icon(
                                ft.icons.CAMERA_ALT,
                                size=80,
                                color=ft.colors.PRIMARY,
                            ),
                            ft.Text(
                                "Quick Receipt Scan",
                                size=24,
                                weight=ft.FontWeight.BOLD,
                                text_align=ft.TextAlign.CENTER,
                            ),
                            ft.Text(
                                "Take a photo of your receipt and we'll extract all the items automatically",
                                size=16,
                                text_align=ft.TextAlign.CENTER,
                                color=ft.colors.ON_SURFACE_VARIANT,
                            ),
                        ],
                        horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                        spacing=16,
                    ),
                    padding=ft.padding.all(32),
                    alignment=ft.alignment.center,
                ),
                ft.Container(height=20),  # Spacer
                ft.ElevatedButton(
                    content=ft.Row(
                        [
                            ft.Icon(ft.icons.CAMERA_ALT),
                            ft.Text("Scan Receipt", size=18),
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=8,
                    ),
                    style=ft.ButtonStyle(
                        padding=ft.padding.symmetric(horizontal=32, vertical=16),
                        shape=ft.RoundedRectangleBorder(radius=12),
                    ),
                    on_click=self.start_receipt_scan,
                    width=300,
                ),
                ft.Container(height=16),
                ft.OutlinedButton(
                    content=ft.Row(
                        [
                            ft.Icon(ft.icons.PHOTO_LIBRARY),
                            ft.Text("Choose from Gallery", size=16),
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=8,
                    ),
                    style=ft.ButtonStyle(
                        padding=ft.padding.symmetric(horizontal=32, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=12),
                    ),
                    on_click=self.choose_from_gallery,
                    width=300,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            expand=True,
        )

    def start_receipt_scan(self, _):
        """Start camera for receipt scanning - for now, use file picker"""
        self.file_picker.pick_files(
            allow_multiple=False,
            allowed_extensions=["jpg", "jpeg", "png", "webp"],
            dialog_title="Select Receipt Image"
        )

    def choose_from_gallery(self, _):
        """Choose image from gallery"""
        self.file_picker.pick_files(
            allow_multiple=False,
            allowed_extensions=["jpg", "jpeg", "png", "webp"],
            dialog_title="Choose Receipt from Gallery"
        )

    def on_file_picked(self, e: ft.FilePickerResultEvent):
        """Handle file selection for receipt scanning"""
        if e.files and len(e.files) > 0:
            self.show_loading("Processing receipt...")

            try:
                file_path = e.files[0].path
                with open(file_path, "rb") as f:
                    image_data = base64.b64encode(f.read()).decode("utf-8")

                # Process the receipt
                receipt_data = process_receipt_image(image_data)
                self.hide_loading()

                if receipt_data:
                    self.show_receipt_confirmation(receipt_data)
                else:
                    self.show_error("Failed to process receipt. Please try again.")

            except Exception as ex:
                self.hide_loading()
                self.show_error(f"Error processing image: {str(ex)}")

    def show_loading(self, message: str):
        """Show loading dialog"""
        self.loading_dialog = ft.AlertDialog(
            modal=True,
            content=ft.Column(
                [
                    ft.ProgressRing(),
                    ft.Text(message, text_align=ft.TextAlign.CENTER),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                tight=True,
            ),
        )
        self.page.dialog = self.loading_dialog
        self.loading_dialog.open = True
        self.page.update()

    def hide_loading(self):
        """Hide loading dialog"""
        if hasattr(self, 'loading_dialog'):
            self.loading_dialog.open = False
            self.page.update()

    def show_error(self, message: str):
        """Show error message"""
        self.page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.colors.ERROR,
            )
        )

    def show_success(self, message: str):
        """Show success message"""
        self.page.show_snack_bar(
            ft.SnackBar(
                content=ft.Text(message),
                bgcolor=ft.colors.GREEN,
            )
        )

    def show_receipt_confirmation(self, receipt_data):
        """Show confirmation dialog for scanned receipt items"""
        items_list = ft.Column(spacing=8)

        for item in receipt_data.items:
            items_list.controls.append(
                ft.Card(
                    content=ft.Container(
                        content=ft.Column(
                            [
                                ft.Text(item.name, weight=ft.FontWeight.BOLD),
                                ft.Text(f"Category: {item.category}"),
                                ft.Text(f"Price: ${item.price:.2f} x {item.quantity}"),
                            ],
                            spacing=4,
                        ),
                        padding=ft.padding.all(12),
                    ),
                )
            )

        self.confirmation_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Confirm Receipt Items"),
            content=ft.Column(
                [
                    ft.Text(f"Shop: {receipt_data.shop_name or 'Unknown'}"),
                    ft.Text(f"Date: {receipt_data.date}"),
                    ft.Text(f"Total: ${receipt_data.total:.2f}"),
                    ft.Divider(),
                    ft.Text("Items:", weight=ft.FontWeight.BOLD),
                    ft.Container(
                        content=items_list,
                        height=300,
                    ),
                ],
                scroll=ft.ScrollMode.AUTO,
            ),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_confirmation),
                ft.ElevatedButton(
                    "Add All Items",
                    on_click=lambda _: self.save_receipt_items(receipt_data)
                ),
            ],
        )
        self.page.dialog = self.confirmation_dialog
        self.confirmation_dialog.open = True
        self.page.update()

    def close_confirmation(self, _):
        """Close confirmation dialog"""
        self.confirmation_dialog.open = False
        self.page.update()

    def save_receipt_items(self, receipt_data):
        """Save all items from the receipt"""
        try:
            for item in receipt_data.items:
                add_expense(
                    item.name,
                    item.category,
                    item.price,
                    item.quantity,
                    receipt_data.date,
                    receipt_data.time,
                    receipt_data.shop_name
                )

            self.close_confirmation(None)
            self.show_success(f"Added {len(receipt_data.items)} items successfully!")

            # Switch to expenses tab to show the added items
            self.current_tab = 1
            self.navigation_bar.selected_index = 1
            self.content_container.content = self.build_expenses_tab()
            self.update()

        except Exception as ex:
            self.show_error(f"Error saving items: {str(ex)}")

    def build_expenses_tab(self):
        """Build the expenses list tab"""
        expenses = get_all_expenses()

        if not expenses:
            return ft.Column(
                [
                    ft.Container(
                        content=ft.Column(
                            [
                                ft.Icon(
                                    ft.icons.RECEIPT_LONG,
                                    size=64,
                                    color=ft.colors.ON_SURFACE_VARIANT,
                                ),
                                ft.Text(
                                    "No expenses yet",
                                    size=20,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Text(
                                    "Start by scanning a receipt or adding an expense manually",
                                    text_align=ft.TextAlign.CENTER,
                                    color=ft.colors.ON_SURFACE_VARIANT,
                                ),
                            ],
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=16,
                        ),
                        padding=ft.padding.all(32),
                        alignment=ft.alignment.center,
                        expand=True,
                    ),
                ],
                expand=True,
            )

        # Create expense list
        expense_list = ft.ListView(spacing=8, expand=True)

        for expense in expenses:
            expense_id, name, category, price, quantity, date, _, shop_name = expense
            total_price = price * quantity

            expense_card = ft.Card(
                content=ft.Container(
                    content=ft.Column(
                        [
                            ft.Row(
                                [
                                    ft.Column(
                                        [
                                            ft.Text(
                                                name,
                                                size=16,
                                                weight=ft.FontWeight.BOLD,
                                            ),
                                            ft.Text(
                                                category,
                                                size=12,
                                                color=ft.colors.PRIMARY,
                                            ),
                                        ],
                                        expand=True,
                                        spacing=2,
                                    ),
                                    ft.Column(
                                        [
                                            ft.Text(
                                                f"${total_price:.2f}",
                                                size=16,
                                                weight=ft.FontWeight.BOLD,
                                                text_align=ft.TextAlign.RIGHT,
                                            ),
                                            ft.Text(
                                                f"${price:.2f} x {quantity}",
                                                size=12,
                                                color=ft.colors.ON_SURFACE_VARIANT,
                                                text_align=ft.TextAlign.RIGHT,
                                            ),
                                        ],
                                        spacing=2,
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                            ft.Row(
                                [
                                    ft.Text(
                                        date,
                                        size=12,
                                        color=ft.colors.ON_SURFACE_VARIANT,
                                    ),
                                    ft.Text(
                                        shop_name or "",
                                        size=12,
                                        color=ft.colors.ON_SURFACE_VARIANT,
                                    ),
                                    ft.Row(
                                        [
                                            ft.IconButton(
                                                ft.icons.EDIT,
                                                icon_size=16,
                                                on_click=lambda _, eid=expense_id: self.show_edit_expense_dialog(eid),
                                            ),
                                            ft.IconButton(
                                                ft.icons.DELETE,
                                                icon_size=16,
                                                icon_color=ft.colors.ERROR,
                                                on_click=lambda _, eid=expense_id: self.delete_expense_item(eid),
                                            ),
                                        ],
                                        spacing=0,
                                    ),
                                ],
                                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                            ),
                        ],
                        spacing=8,
                    ),
                    padding=ft.padding.all(16),
                ),
                on_click=lambda _, eid=expense_id: self.show_edit_expense_dialog(eid),
            )
            expense_list.controls.append(expense_card)

        return ft.Column(
            [
                ft.Row(
                    [
                        ft.Text(
                            "Your Expenses",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                        ),
                        ft.IconButton(
                            ft.icons.SEARCH,
                            on_click=self.show_search_dialog,
                        ),
                    ],
                    alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                ),
                expense_list,
            ],
            spacing=16,
            expand=True,
        )

    def build_add_tab(self):
        """Build the manual add expense tab"""
        return ft.Column(
            [
                ft.Text(
                    "Add Expense Manually",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                ),
                ft.Container(height=16),
                self.build_expense_form(),
            ],
            spacing=16,
            expand=True,
        )

    def build_expense_form(self):
        """Build the expense form"""
        self.name_field = ft.TextField(
            label="Item Name",
            hint_text="e.g., Coffee, Lunch, Gas",
            border_radius=12,
        )

        self.category_field = ft.Dropdown(
            label="Category",
            border_radius=12,
            options=[
                ft.dropdown.Option("Food & Dining"),
                ft.dropdown.Option("Transportation"),
                ft.dropdown.Option("Utilities"),
                ft.dropdown.Option("Entertainment"),
                ft.dropdown.Option("Shopping"),
                ft.dropdown.Option("Healthcare"),
                ft.dropdown.Option("Education"),
                ft.dropdown.Option("Travel"),
                ft.dropdown.Option("Housing"),
                ft.dropdown.Option("Other"),
            ]
        )

        self.price_field = ft.TextField(
            label="Price",
            hint_text="0.00",
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=12,
        )

        self.quantity_field = ft.TextField(
            label="Quantity",
            hint_text="1",
            value="1",
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=12,
        )

        self.date_field = ft.TextField(
            label="Date (dd/mm/yyyy)",
            hint_text=datetime.now().strftime("%d/%m/%Y"),
            value=datetime.now().strftime("%d/%m/%Y"),
            border_radius=12,
        )

        return ft.Column(
            [
                self.name_field,
                self.category_field,
                ft.Row(
                    [
                        ft.Container(self.price_field, expand=2),
                        ft.Container(self.quantity_field, expand=1),
                    ],
                    spacing=16,
                ),
                self.date_field,
                ft.Container(height=24),
                ft.ElevatedButton(
                    content=ft.Row(
                        [
                            ft.Icon(ft.icons.SAVE),
                            ft.Text("Add Expense", size=16),
                        ],
                        alignment=ft.MainAxisAlignment.CENTER,
                        spacing=8,
                    ),
                    style=ft.ButtonStyle(
                        padding=ft.padding.symmetric(horizontal=32, vertical=16),
                        shape=ft.RoundedRectangleBorder(radius=12),
                    ),
                    on_click=self.add_manual_expense,
                    width=300,
                ),
            ],
            spacing=16,
        )

    def add_manual_expense(self, _):
        """Add a manual expense"""
        try:
            # Validate inputs
            if not all([
                self.name_field.value,
                self.category_field.value,
                self.price_field.value,
                self.quantity_field.value,
                self.date_field.value
            ]):
                self.show_error("Please fill in all fields")
                return

            add_expense(
                self.name_field.value,
                self.category_field.value,
                float(self.price_field.value),
                int(self.quantity_field.value),
                self.date_field.value
            )

            # Clear form
            self.name_field.value = ""
            self.category_field.value = None
            self.price_field.value = ""
            self.quantity_field.value = "1"
            self.date_field.value = datetime.now().strftime("%d/%m/%Y")

            self.show_success("Expense added successfully!")
            self.update()

        except ValueError as ex:
            self.show_error(f"Invalid input: {str(ex)}")
        except Exception as ex:
            self.show_error(f"Error adding expense: {str(ex)}")

    def show_search_dialog(self, _):
        """Show search dialog for expenses"""
        # This could be implemented later for filtering expenses
        self.show_error("Search functionality coming soon!")

    def build_reports_tab(self):
        """Build the reports tab with charts"""
        expenses = get_all_expenses()

        if not expenses:
            return ft.Column(
                [
                    ft.Container(
                        content=ft.Column(
                            [
                                ft.Icon(
                                    ft.icons.ANALYTICS,
                                    size=64,
                                    color=ft.colors.ON_SURFACE_VARIANT,
                                ),
                                ft.Text(
                                    "No data for reports",
                                    size=20,
                                    weight=ft.FontWeight.BOLD,
                                ),
                                ft.Text(
                                    "Add some expenses to see your spending patterns",
                                    text_align=ft.TextAlign.CENTER,
                                    color=ft.colors.ON_SURFACE_VARIANT,
                                ),
                            ],
                            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                            spacing=16,
                        ),
                        padding=ft.padding.all(32),
                        alignment=ft.alignment.center,
                        expand=True,
                    ),
                ],
                expand=True,
            )

        # Calculate summary statistics
        df = pd.DataFrame(expenses, columns=['id', 'name', 'category', 'price', 'quantity', 'date', 'time', 'shop_name'])
        df['total_price'] = df['price'] * df['quantity']

        total_spent = df['total_price'].sum()
        avg_expense = df['total_price'].mean()
        total_items = len(df)

        # Category breakdown
        category_totals = df.groupby('category')['total_price'].sum().sort_values(ascending=False)

        # Create summary cards
        summary_cards = ft.Row(
            [
                self.create_summary_card("Total Spent", f"${total_spent:.2f}", ft.icons.ATTACH_MONEY),
                self.create_summary_card("Avg Expense", f"${avg_expense:.2f}", ft.icons.TRENDING_UP),
                self.create_summary_card("Total Items", str(total_items), ft.icons.RECEIPT),
            ],
            spacing=8,
        )

        # Category breakdown list
        category_list = ft.Column(spacing=8)
        for category, amount in category_totals.head(5).items():
            percentage = (amount / total_spent) * 100
            category_list.controls.append(
                ft.Container(
                    content=ft.Row(
                        [
                            ft.Text(category, expand=True),
                            ft.Text(f"${amount:.2f}"),
                            ft.Text(f"{percentage:.1f}%", color=ft.colors.PRIMARY),
                        ],
                        alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                    ),
                    padding=ft.padding.all(12),
                    border_radius=8,
                    bgcolor=ft.colors.SURFACE_VARIANT,
                )
            )

        return ft.Column(
            [
                ft.Text(
                    "Expense Reports",
                    size=24,
                    weight=ft.FontWeight.BOLD,
                ),
                summary_cards,
                ft.Container(height=16),
                ft.Text(
                    "Top Categories",
                    size=18,
                    weight=ft.FontWeight.BOLD,
                ),
                category_list,
            ],
            spacing=16,
            expand=True,
            scroll=ft.ScrollMode.AUTO,
        )

    def create_summary_card(self, title: str, value: str, icon):
        """Create a summary card for reports"""
        return ft.Container(
            content=ft.Column(
                [
                    ft.Icon(icon, size=32, color=ft.colors.PRIMARY),
                    ft.Text(value, size=20, weight=ft.FontWeight.BOLD),
                    ft.Text(title, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                ],
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=4,
            ),
            padding=ft.padding.all(16),
            border_radius=12,
            bgcolor=ft.colors.SURFACE_VARIANT,
            expand=True,
        )

    def show_edit_expense_dialog(self, expense_id):
        """Show dialog to edit an expense"""
        expenses = get_all_expenses()
        expense = next((exp for exp in expenses if exp[0] == expense_id), None)
        if not expense:
            return

        self.edit_name_field = ft.TextField(
            label="Name",
            value=expense[1],
            border_radius=12,
        )
        self.edit_category_field = ft.Dropdown(
            label="Category",
            value=expense[2],
            border_radius=12,
            options=[
                ft.dropdown.Option("Food & Dining"),
                ft.dropdown.Option("Transportation"),
                ft.dropdown.Option("Utilities"),
                ft.dropdown.Option("Entertainment"),
                ft.dropdown.Option("Shopping"),
                ft.dropdown.Option("Healthcare"),
                ft.dropdown.Option("Education"),
                ft.dropdown.Option("Travel"),
                ft.dropdown.Option("Housing"),
                ft.dropdown.Option("Other"),
            ]
        )
        self.edit_price_field = ft.TextField(
            label="Price",
            value=str(expense[3]),
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=12,
        )
        self.edit_quantity_field = ft.TextField(
            label="Quantity",
            value=str(expense[4]),
            keyboard_type=ft.KeyboardType.NUMBER,
            border_radius=12,
        )
        self.edit_date_field = ft.TextField(
            label="Date (dd/mm/yyyy)",
            value=expense[5],
            border_radius=12,
        )

        self.edit_dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("Edit Expense"),
            content=ft.Column([
                self.edit_name_field,
                self.edit_category_field,
                ft.Row(
                    [
                        ft.Container(self.edit_price_field, expand=2),
                        ft.Container(self.edit_quantity_field, expand=1),
                    ],
                    spacing=16,
                ),
                self.edit_date_field,
            ]),
            actions=[
                ft.TextButton("Cancel", on_click=self.close_edit_dialog),
                ft.ElevatedButton("Save", on_click=lambda _: self.save_edited_expense(expense_id)),
            ],
        )
        self.page.dialog = self.edit_dialog
        self.edit_dialog.open = True
        self.page.update()

    def save_edited_expense(self, expense_id):
        """Save edited expense"""
        try:
            update_expense(
                expense_id,
                self.edit_name_field.value,
                self.edit_category_field.value,
                float(self.edit_price_field.value),
                int(self.edit_quantity_field.value),
                self.edit_date_field.value
            )
            self.close_edit_dialog(None)
            self.show_success("Expense updated successfully!")

            # Refresh the current view
            if self.current_tab == 1:  # Expenses tab
                self.content_container.content = self.build_expenses_tab()
                self.update()

        except ValueError as ex:
            self.show_error(f"Invalid input: {str(ex)}")
        except Exception as ex:
            self.show_error(f"Error updating expense: {str(ex)}")

    def delete_expense_item(self, expense_id):
        """Delete an expense item"""
        try:
            delete_expense(expense_id)
            self.show_success("Expense deleted successfully!")

            # Refresh the current view
            if self.current_tab == 1:  # Expenses tab
                self.content_container.content = self.build_expenses_tab()
                self.update()

        except Exception as ex:
            self.show_error(f"Error deleting expense: {str(ex)}")

    def close_edit_dialog(self, _):
        """Close edit dialog"""
        if hasattr(self, 'edit_dialog'):
            self.edit_dialog.open = False
            self.page.update()