import base64
import os
from google import genai
from models import Receipt

# This is the modern and recommended way to use the google-genai library.
def process_receipt_image(image_data: str) -> Receipt | None:
    try:
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            print("FATAL: GEMINI_API_KEY environment variable is not set.")
            raise ValueError("GEMINI_API_KEY is not configured.")

        # Configure the library with your API key
        genai.configure(api_key=api_key)

        # Define the generation configuration to force JSON output
        generation_config = genai.GenerationConfig(
            response_mime_type="application/json",
        )

        # Define the model. 'gemini-1.5-flash' is the latest fast model.
        # The system_instruction tells the model exactly how to behave and what schema to use.
        model = genai.GenerativeModel(
            model_name='gemini-1.5-flash',
            generation_config=generation_config,
            system_instruction="""You are an intelligent expense tracker. A user has provided a photo of a receipt.
Your task is to extract all purchased items, the purchase date, total amount, and if available, the shop name and time.
Provide the output *only* in a clean JSON format. Do not add any conversational text or markdown formatting like ```json.
The JSON object must strictly follow this schema:
{
  "type": "object",
  "properties": {
    "items": {
      "type": "array",
      "description": "List of items in the receipt",
      "items": {
        "type": "object",
        "properties": {
          "name": {"type": "string", "description": "Name or description of the item"},
          "category": {
            "type": "string",
            "description": "Category of the item",
            "enum": ["Food & Dining", "Transportation", "Utilities", "Entertainment", "Shopping", "Healthcare", "Education", "Travel", "Housing", "Other"]
          },
          "price": {"type": "number", "description": "Price of the item"},
          "quantity": {"type": "integer", "description": "Quantity of the item"}
        },
        "required": ["name", "category", "price", "quantity"]
      }
    },
    "date": {"type": "string", "description": "Date of the purchase in dd/mm/yyyy format"},
    "time": {"type": "string", "description": "Time of the purchase (optional)"},
    "shop_name": {"type": "string", "description": "Name of the shop (optional)"},
    "total": {"type": "number", "description": "Total amount of the purchase"}
  },
  "required": ["items", "date", "total"]
}"""
        )

        # Prepare the image for the API call
        image_part = {
            "mime_type": "image/jpeg", # Using jpeg is a safe default
            "data": base64.b64decode(image_data)
        }

        # Generate content by sending the image
        response = model.generate_content([image_part])

        # The response.text should be a JSON string, which Pydantic can validate
        return Receipt.model_validate_json(response.text)

    except Exception as e:
        print(f"An unexpected error occurred while processing the receipt with Gemini: {e}")
        # It's helpful to print the response text if it exists and failed parsing
        if 'response' in locals() and hasattr(response, 'text'):
            print("Gemini response text:", response.text)
        return None