import sqlite3

def init_db():
    conn = sqlite3.connect('expenses.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            category TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL,
            date TEXT NOT NULL,
            time TEXT,
            shop_name TEXT
        )
    ''')
    conn.commit()
    conn.close()

def add_expense(name, category, price, quantity, date, time=None, shop_name=None):
    conn = sqlite3.connect('expenses.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO expenses (name, category, price, quantity, date, time, shop_name)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (name, category, price, quantity, date, time, shop_name))
    conn.commit()
    conn.close()

def get_all_expenses():
    conn = sqlite3.connect('expenses.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM expenses')
    expenses = cursor.fetchall()
    conn.close()
    return expenses

def update_expense(expense_id, name, category, price, quantity, date, time=None, shop_name=None):
    conn = sqlite3.connect('expenses.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE expenses
        SET name = ?, category = ?, price = ?, quantity = ?, date = ?, time = ?, shop_name = ?
        WHERE id = ?
    ''', (name, category, price, quantity, date, time, shop_name, expense_id))
    conn.commit()
    conn.close()

def delete_expense(expense_id):
    conn = sqlite3.connect('expenses.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM expenses WHERE id = ?', (expense_id,))
    conn.commit()
    conn.close()